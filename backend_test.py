#!/usr/bin/env python3
"""
Comprehensive Backend API Testing for Genrec AI
Tests all core API endpoints with various scenarios
"""

import requests
import json
import sys
from datetime import datetime
import time

# Get backend URL from frontend .env
BACKEND_URL = "https://ad7661af-6570-4a63-a31e-e3ea5ef348a4.preview.emergentagent.com"
API_BASE = f"{BACKEND_URL}/api"

class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_test_header(test_name):
    print(f"\n{Colors.BLUE}{Colors.BOLD}{'='*60}{Colors.ENDC}")
    print(f"{Colors.BLUE}{Colors.BOLD}Testing: {test_name}{Colors.ENDC}")
    print(f"{Colors.BLUE}{Colors.BOLD}{'='*60}{Colors.ENDC}")

def print_success(message):
    print(f"{Colors.GREEN}✅ {message}{Colors.ENDC}")

def print_error(message):
    print(f"{Colors.RED}❌ {message}{Colors.ENDC}")

def print_warning(message):
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.ENDC}")

def print_info(message):
    print(f"{Colors.BLUE}ℹ️  {message}{Colors.ENDC}")

def test_root_endpoint():
    """Test GET /api/ - Root endpoint health check"""
    print_test_header("Root Endpoint Health Check")
    
    try:
        response = requests.get(f"{API_BASE}/", timeout=10)
        print_info(f"Status Code: {response.status_code}")
        print_info(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if "message" in data and "Genrec AI Backend API" in data["message"]:
                print_success("Root endpoint working correctly")
                return True
            else:
                print_error("Root endpoint response format incorrect")
                return False
        else:
            print_error(f"Root endpoint failed with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_error(f"Root endpoint request failed: {str(e)}")
        return False

def test_contact_submit_valid():
    """Test POST /api/contact/submit with valid Gmail address"""
    print_test_header("Contact Submit - Valid Gmail")
    
    test_data = {
        "email": "<EMAIL>"
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/contact/submit",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print_info(f"Status Code: {response.status_code}")
        print_info(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            required_fields = ["success", "message", "id"]
            
            if all(field in data for field in required_fields):
                if data["success"] and data["id"]:
                    print_success("Valid Gmail submission working correctly")
                    print_info(f"Generated ID: {data['id']}")
                    return True, data["id"]
                else:
                    print_error("Response indicates failure despite 200 status")
                    return False, None
            else:
                print_error(f"Response missing required fields: {required_fields}")
                return False, None
        else:
            print_error(f"Valid Gmail submission failed with status {response.status_code}")
            return False, None
            
    except requests.exceptions.RequestException as e:
        print_error(f"Valid Gmail submission request failed: {str(e)}")
        return False, None

def test_contact_submit_invalid_domain():
    """Test POST /api/contact/submit with invalid email domain (should fail)"""
    print_test_header("Contact Submit - Invalid Domain (Yahoo)")
    
    test_data = {
        "email": "<EMAIL>"
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/contact/submit",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print_info(f"Status Code: {response.status_code}")
        print_info(f"Response: {response.text}")
        
        if response.status_code == 422:  # FastAPI validation error
            data = response.json()
            if "detail" in data:
                print_success("Invalid domain correctly rejected")
                return True
            else:
                print_warning("Invalid domain rejected but error message unclear")
                return True
        else:
            print_error(f"Invalid domain should return 422, got {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_error(f"Invalid domain test request failed: {str(e)}")
        return False

def test_contact_submit_malformed():
    """Test POST /api/contact/submit with malformed email (should fail)"""
    print_test_header("Contact Submit - Malformed Email")
    
    test_data = {
        "email": "invalid-email"
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/contact/submit",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print_info(f"Status Code: {response.status_code}")
        print_info(f"Response: {response.text}")
        
        if response.status_code == 422:  # FastAPI validation error
            print_success("Malformed email correctly rejected")
            return True
        else:
            print_error(f"Malformed email should return 422, got {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_error(f"Malformed email test request failed: {str(e)}")
        return False

def test_contact_submit_missing_field():
    """Test POST /api/contact/submit with missing email field"""
    print_test_header("Contact Submit - Missing Email Field")
    
    test_data = {}
    
    try:
        response = requests.post(
            f"{API_BASE}/contact/submit",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print_info(f"Status Code: {response.status_code}")
        print_info(f"Response: {response.text}")
        
        if response.status_code == 422:  # FastAPI validation error
            print_success("Missing email field correctly rejected")
            return True
        else:
            print_error(f"Missing email should return 422, got {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_error(f"Missing field test request failed: {str(e)}")
        return False

def test_contact_submit_malformed_json():
    """Test POST /api/contact/submit with malformed JSON"""
    print_test_header("Contact Submit - Malformed JSON")
    
    try:
        response = requests.post(
            f"{API_BASE}/contact/submit",
            data="invalid json",
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print_info(f"Status Code: {response.status_code}")
        print_info(f"Response: {response.text}")
        
        if response.status_code in [400, 422]:
            print_success("Malformed JSON correctly rejected")
            return True
        else:
            print_error(f"Malformed JSON should return 400/422, got {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_error(f"Malformed JSON test request failed: {str(e)}")
        return False

def test_get_submissions():
    """Test GET /api/contact/submissions - Retrieve all submissions"""
    print_test_header("Get Contact Submissions")
    
    try:
        response = requests.get(f"{API_BASE}/contact/submissions", timeout=10)
        
        print_info(f"Status Code: {response.status_code}")
        print_info(f"Response length: {len(response.text)} characters")
        
        if response.status_code == 200:
            data = response.json()
            
            if isinstance(data, list):
                print_success(f"Successfully retrieved {len(data)} submissions")
                
                # Check structure of submissions if any exist
                if len(data) > 0:
                    first_submission = data[0]
                    required_fields = ["id", "email", "timestamp", "status"]
                    
                    if all(field in first_submission for field in required_fields):
                        print_success("Submission data structure is correct")
                        print_info(f"Sample submission: {json.dumps(first_submission, indent=2, default=str)}")
                        return True
                    else:
                        print_error(f"Submission missing required fields: {required_fields}")
                        return False
                else:
                    print_info("No submissions found (empty array)")
                    return True
            else:
                print_error("Response should be an array")
                return False
        else:
            print_error(f"Get submissions failed with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_error(f"Get submissions request failed: {str(e)}")
        return False

def test_cors_headers():
    """Test CORS configuration"""
    print_test_header("CORS Configuration")
    
    try:
        response = requests.options(f"{API_BASE}/", timeout=10)
        print_info(f"OPTIONS Status Code: {response.status_code}")
        
        # Check for CORS headers
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
        }
        
        print_info(f"CORS Headers: {cors_headers}")
        
        if cors_headers['Access-Control-Allow-Origin']:
            print_success("CORS is configured")
            return True
        else:
            print_warning("CORS headers not found in OPTIONS response")
            return True  # Not critical for functionality
            
    except requests.exceptions.RequestException as e:
        print_warning(f"CORS test failed: {str(e)}")
        return True  # Not critical

def run_all_tests():
    """Run all backend API tests"""
    print(f"{Colors.BOLD}Genrec AI Backend API Testing Suite{Colors.ENDC}")
    print(f"Testing against: {API_BASE}")
    print(f"Timestamp: {datetime.now()}")
    
    test_results = {}
    
    # Core functionality tests
    test_results['root_endpoint'] = test_root_endpoint()
    test_results['valid_gmail'] = test_contact_submit_valid()[0]
    test_results['invalid_domain'] = test_contact_submit_invalid_domain()
    test_results['malformed_email'] = test_contact_submit_malformed()
    test_results['missing_field'] = test_contact_submit_missing_field()
    test_results['malformed_json'] = test_contact_submit_malformed_json()
    test_results['get_submissions'] = test_get_submissions()
    test_results['cors'] = test_cors_headers()
    
    # Summary
    print_test_header("Test Summary")
    
    passed = sum(1 for result in test_results.values() if result)
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "PASS" if result else "FAIL"
        color = Colors.GREEN if result else Colors.RED
        print(f"{color}{status:4} - {test_name.replace('_', ' ').title()}{Colors.ENDC}")
    
    print(f"\n{Colors.BOLD}Overall Result: {passed}/{total} tests passed{Colors.ENDC}")
    
    if passed == total:
        print_success("All tests passed! Backend API is working correctly.")
        return True
    else:
        print_error(f"{total - passed} tests failed. Backend needs attention.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)