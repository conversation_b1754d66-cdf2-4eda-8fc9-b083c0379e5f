# Genrec AI Backend Integration Contracts

## API Endpoints to Implement

### 1. Contact Submissions API
```
POST /api/contact/submit
Body: { email: string }
Response: { success: boolean, message: string, id: string }
```

### 2. Get Contact Submissions (Admin)
```
GET /api/contact/submissions
Response: { submissions: Array<ContactSubmission> }
```

---

## Database Models

### ContactSubmission Model
```javascript
{
  id: string (UUID),
  email: string (validated Gmail),
  timestamp: DateTime,
  status: 'pending' | 'contacted' | 'completed',
  ip_address: string (optional),
  user_agent: string (optional)
}
```

---

## Frontend Mock Data to Replace

### Current Mock Data (`/app/frontend/src/mock.js`):
1. **mockContactSubmissions** → Replace with real API calls
2. **mockSubmitContact()** → Replace with actual POST to `/api/contact/submit`
3. **mockValidateEmail()** → Keep client-side, add server-side validation

### Frontend Components to Update:
1. **ContactSection.js** → Replace mock submission with real API call
2. **Remove mock.js imports** and replace with axios API calls

---

## Backend Implementation Plan

### Phase 1: Core API
- [x] MongoDB ContactSubmission model
- [x] POST /api/contact/submit endpoint with validation
- [x] GET /api/contact/submissions endpoint
- [x] Error handling and logging
- [x] Email format validation (server-side)

### Phase 2: Email Notifications
- [x] Email notification system (SendGrid/Gmail SMTP)
- [x] Admin notification when new submission received
- [x] Auto-reply confirmation to user

### Phase 3: Admin Features
- [ ] Simple admin dashboard to view submissions
- [ ] Mark submissions as contacted/completed
- [ ] Export submissions to CSV

---

## Integration Points

### Frontend Changes:
```javascript
// Replace mock submission
const handleSubmit = async (email) => {
  const response = await axios.post(`${API}/contact/submit`, { email });
  return response.data;
};
```

### Environment Variables:
```
# Backend (.env)
MONGO_URL=mongodb://localhost:27017/genrec_ai
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>  
SMTP_PASS=app_password
ADMIN_EMAIL=<EMAIL>
```

---

## Testing Checklist

### Backend API Testing:
- [x] POST /api/contact/submit with valid Gmail
- [x] POST /api/contact/submit with invalid email (should fail)
- [x] GET /api/contact/submissions returns correct data
- [x] Database properly stores submissions
- [x] Email notifications sent correctly

### Frontend Integration Testing:
- [x] Contact form submits to real backend
- [x] Toast notifications show success/error messages
- [x] Form resets after successful submission
- [x] Email validation works on both client and server
- [x] No more mock data dependencies

### End-to-End Testing:
- [x] User can submit Gmail address
- [x] Submission stored in MongoDB
- [x] Admin receives email notification
- [x] User sees success message
- [x] Multiple submissions work correctly

---

## Performance & Security

### Security Features:
- Rate limiting on contact submission
- Email sanitization and validation
- CORS configuration
- Input validation and sanitization

### Performance:
- Database indexing on timestamp
- Async/await for all database operations
- Error logging and monitoring
- Response compression

---

## Deployment Readiness

### Production Considerations:
- Environment-specific configurations
- Database connection pooling
- Error monitoring (optional: Sentry)
- SSL/HTTPS enforcement
- API rate limiting

This contract ensures seamless integration between frontend and backend while maintaining the existing user experience.