from fastapi import <PERSON><PERSON><PERSON>, API<PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.responses import JSONResponse
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
import re
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from pathlib import Path
from pydantic import BaseModel, Field, validator
from typing import List, Optional
import uuid
from datetime import datetime


ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# MongoDB connection
mongo_url = os.environ['MONGO_URL']
client = AsyncIOMotorClient(mongo_url)
db = client[os.environ['DB_NAME']]

# Create the main app without a prefix
app = FastAPI(title="Genrec AI API", version="1.0.0")

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")


# Define Models
class ContactSubmission(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    email: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    status: str = Field(default="pending")
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    
    @validator('email')
    def validate_gmail(cls, v):
        gmail_pattern = r'^[a-zA-Z0-9._%+-]+@gmail\.com$'
        if not re.match(gmail_pattern, v.lower()):
            raise ValueError('Email must be a valid Gmail address (@gmail.com)')
        return v.lower()

class ContactSubmissionCreate(BaseModel):
    email: str
    
    @validator('email')
    def validate_gmail(cls, v):
        gmail_pattern = r'^[a-zA-Z0-9._%+-]+@gmail\.com$'
        if not re.match(gmail_pattern, v.lower()):
            raise ValueError('Email must be a valid Gmail address (@gmail.com)')
        return v.lower()

class ContactSubmissionResponse(BaseModel):
    success: bool
    message: str
    id: Optional[str] = None

class StatusCheck(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    client_name: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class StatusCheckCreate(BaseModel):
    client_name: str


# Email notification function
async def send_email_notification(email: str, submission_id: str):
    """Send email notification to admin and confirmation to user"""
    try:
        # Email configuration (using environment variables)
        smtp_host = os.environ.get('SMTP_HOST', 'smtp.gmail.com')
        smtp_port = int(os.environ.get('SMTP_PORT', '587'))
        smtp_user = os.environ.get('SMTP_USER', '<EMAIL>')
        smtp_pass = os.environ.get('SMTP_PASS', '')
        admin_email = os.environ.get('ADMIN_EMAIL', '<EMAIL>')
        
        if not smtp_pass:
            logging.warning("SMTP credentials not configured - skipping email notification")
            return
        
        # Create SMTP connection
        server = smtplib.SMTP(smtp_host, smtp_port)
        server.starttls()
        server.login(smtp_user, smtp_pass)
        
        # Send admin notification
        admin_msg = MIMEMultipart()
        admin_msg['From'] = smtp_user
        admin_msg['To'] = admin_email
        admin_msg['Subject'] = 'New Contact Submission - Genrec AI'
        
        admin_body = f"""
        New contact submission received:
        
        Email: {email}
        Submission ID: {submission_id}
        Timestamp: {datetime.utcnow()}
        
        Please follow up with this potential client.
        
        Best regards,
        Genrec AI System
        """
        
        admin_msg.attach(MIMEText(admin_body, 'plain'))
        server.send_message(admin_msg)
        
        # Send user confirmation
        user_msg = MIMEMultipart()
        user_msg['From'] = smtp_user
        user_msg['To'] = email
        user_msg['Subject'] = 'Thank you for contacting Genrec AI'
        
        user_body = f"""
        Hello,
        
        Thank you for your interest in Genrec AI! We've received your contact submission and will reach out to you soon.
        
        We specialize in building intelligent, efficient, and autonomous software systems - from neural networks to real-world applications.
        
        Our team will be in touch within 24 hours to discuss how we can help bring your vision to life.
        
        Best regards,
        The Genrec AI Team
        
        ---
        Architects of intelligent software — from neural systems to real-world impact.
        """
        
        user_msg.attach(MIMEText(user_body, 'plain'))
        server.send_message(user_msg)
        
        server.quit()
        logging.info(f"Email notifications sent successfully for submission {submission_id}")
        
    except Exception as e:
        logging.error(f"Failed to send email notification: {str(e)}")


# Contact submission endpoints
@api_router.post("/contact/submit", response_model=ContactSubmissionResponse)
async def submit_contact(request: Request, submission: ContactSubmissionCreate):
    try:
        # Get client information
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent", "")
        
        # Create submission object
        submission_data = ContactSubmission(
            email=submission.email,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        # Store in database
        result = await db.contact_submissions.insert_one(submission_data.dict())
        
        # Send email notifications (async, don't wait for completion)
        try:
            await send_email_notification(submission.email, submission_data.id)
        except Exception as e:
            logging.error(f"Email notification failed: {str(e)}")
            # Don't fail the API call if email fails
        
        logging.info(f"Contact submission stored: {submission_data.id} from {submission.email}")
        
        return ContactSubmissionResponse(
            success=True,
            message=f"Thank you! We'll reach out to {submission.email} soon.",
            id=submission_data.id
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logging.error(f"Failed to process contact submission: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process submission. Please try again.")

@api_router.get("/contact/submissions", response_model=List[ContactSubmission])
async def get_contact_submissions():
    """Get all contact submissions (admin endpoint)"""
    try:
        submissions = await db.contact_submissions.find().sort("timestamp", -1).to_list(1000)
        return [ContactSubmission(**submission) for submission in submissions]
    except Exception as e:
        logging.error(f"Failed to retrieve submissions: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve submissions")


# Legacy status check endpoints
@api_router.get("/")
async def root():
    return {"message": "Genrec AI Backend API - Ready to power the future!"}

@api_router.post("/status", response_model=StatusCheck)
async def create_status_check(input: StatusCheckCreate):
    status_dict = input.dict()
    status_obj = StatusCheck(**status_dict)
    _ = await db.status_checks.insert_one(status_obj.dict())
    return status_obj

@api_router.get("/status", response_model=List[StatusCheck])
async def get_status_checks():
    status_checks = await db.status_checks.find().to_list(1000)
    return [StatusCheck(**status_check) for status_check in status_checks]

# Include the router in the main app
app.include_router(api_router)

app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@app.on_event("shutdown")
async def shutdown_db_client():
    client.close()
