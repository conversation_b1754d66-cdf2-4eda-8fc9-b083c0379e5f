import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '../components/ui/card';

const AboutPage = () => {
  return (
    <div className="about-page min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            About Genrec AI
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We're revolutionizing the way businesses approach artificial intelligence, 
            making advanced AI solutions accessible, practical, and transformative.
          </p>
        </div>

        {/* Mission Section */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
            <p className="text-lg text-gray-600 mb-4">
              At Genrec AI, we believe that artificial intelligence should empower every business, 
              regardless of size or technical expertise. Our mission is to democratize AI technology 
              and make it accessible to organizations worldwide.
            </p>
            <p className="text-lg text-gray-600">
              We're committed to developing innovative solutions that solve real-world problems, 
              drive efficiency, and unlock new possibilities for growth and innovation.
            </p>
          </div>
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Values</h3>
            <ul className="space-y-3">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700">Innovation-driven solutions</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700">Customer-centric approach</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700">Ethical AI development</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700">Continuous learning</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Team Section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Our Team</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto mb-4"></div>
                <CardTitle>Sarah Johnson</CardTitle>
                <p className="text-gray-600">CEO & Founder</p>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Former AI researcher at Google with 10+ years of experience in machine learning and business strategy.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-24 h-24 bg-gradient-to-r from-green-500 to-blue-600 rounded-full mx-auto mb-4"></div>
                <CardTitle>Michael Chen</CardTitle>
                <p className="text-gray-600">CTO</p>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Expert in AI architecture and scalable systems, previously led engineering teams at Microsoft and OpenAI.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full mx-auto mb-4"></div>
                <CardTitle>Emily Rodriguez</CardTitle>
                <p className="text-gray-600">Head of Product</p>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Product strategist with expertise in user experience and AI product development at leading tech companies.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Company Stats */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">Company Highlights</h2>
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">500+</div>
              <div className="text-gray-600">Clients Served</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">50+</div>
              <div className="text-gray-600">AI Models Deployed</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">99.9%</div>
              <div className="text-gray-600">Uptime</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">24/7</div>
              <div className="text-gray-600">Support</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;
